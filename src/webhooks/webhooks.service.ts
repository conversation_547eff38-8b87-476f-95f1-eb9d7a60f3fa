import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateWebhookDto } from './dto/create-webhook.dto';
import { UpdateWebhookDto } from './dto/update-webhook.dto';
import { EntityManager } from 'typeorm';
import { InjectEntityManager } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import { Webhook } from './entities/webhook.entity';
import { User } from 'src/users/entities/user.entity';
import { NetworksService } from 'src/networks/networks.service';

@Injectable()
export class WebhooksService {
  constructor( 
    @InjectEntityManager()
    private entityManager : EntityManager,
    private networksService: NetworksService
  ){}

  async generateSecurityToken(): Promise<string> {
    const prefix = 'wsLAF_';
    const token = crypto.randomBytes(32).toString('base64');
    return `${prefix}${token}`;
  }

  async checkIfWebhookExists(id: string): Promise<boolean> {
    if (!id) {
      throw new BadRequestException('Invalid webhook ID');
    }
    
    const webhook = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .where('webhook.id = :id AND webhook.deleted_at IS NULL', { id })
      .getOne();
    
    if (!webhook) {
      throw new BadRequestException(`Webhook with ID ${id} not found`);
    }
    return true;
  }

  async validateWebhookOwnership(user_id: number, webhook_name: string){
      const validateWebhookName = await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .where('webhook.user_id = :user_id AND webhook.webhook_name = :webhook_name', {
          user_id,
          webhook_name,
        })
        .getOne();

      if (validateWebhookName) {
        throw new BadRequestException('Webhook name already exists for this user');
      }
  }

  async createWebhook(createWebhookDto: CreateWebhookDto , user_id: number): Promise<any> {
    // use auth to check for user 
    try {
      // check if network is valid
      await this.networksService.validateNetwork(createWebhookDto.network_name);
      // check if user has a webhook with this name
      await this.validateWebhookOwnership( user_id, createWebhookDto.webhook_name);

      // check network name valid and get networkData for block number
      await this.networksService.findOneWithName(createWebhookDto.network_name);

      // get latest block number from network and set start position to that block number

      const securityToken = await this.generateSecurityToken();
      const currentSequence = await this.getLatestBlockSequence();

      const destinationAttributes = {
        url: createWebhookDto.destination_attributes.url,
        security_token: securityToken,
        compression: createWebhookDto.destination_attributes.compression
      };

      const monitorAddresses = {
        wallets: createWebhookDto.wallets
      };

      // Get user information for notification email
      const user = await this.entityManager
        .createQueryBuilder(User, 'user')
        .where('user.id = :user_id', { user_id: user_id })
        .getOne();

      if (!user) {
        throw new BadRequestException('User not found');
      }

      const insertResult = await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Webhook)
        .values({
          user_id: user_id,
          webhook_name: createWebhookDto.webhook_name,
          webhook_url: createWebhookDto.destination_attributes.url,
          security_tokens: securityToken,
          start_position: currentSequence,
          status: createWebhookDto.status as any, // Cast to enum type
        })
        .returning('*')
        .execute();

        const savedWebhook = insertResult.generatedMaps[0] as Webhook;

        return {
        id: savedWebhook.id,
        name: savedWebhook.webhook_name,
        status: savedWebhook.status,
        created_at: savedWebhook.created_at,
        destination_attributes: destinationAttributes,
        network: createWebhookDto.network_name,
        notification_email: user.email,
        sequence: parseInt(currentSequence),
        updated_at: savedWebhook.updated_at,
        monitorAddresses: monitorAddresses
      };
    } catch (error) {
      throw new BadRequestException("Failed to create webhook", error.message);
    }
  }

  // sequence or block number get from quicknode see if there is function return current blocknumber 
  // create helper function to get latest block number from diffrenet networks ( all under sam function getblockNumber(network_name) )
  // use this function to set start position when creating webhook
  // use this function to do sequence 

  async getLatestBlockNumber(network_name: string): Promise<string> {
    return "hello";
  }

  async getLatestBlockSequence(): Promise<string> {
    // from the right network pass in network id and filter 
    const latestTransaction = await this.entityManager
      .createQueryBuilder('transactions', 'tx')
      .select('MAX(CAST(tx.blockNumber as BIGINT))', 'max_block')
      .getRawOne();
    
    return latestTransaction?.max_block?.toString() || '0';
  }


  // whitelist endpoint for user to add endpoints
  // if whitlist endpoint array is empty or null, send to all endpoints
  // if not empty or null, check if endpoint is in whitelist array
  // if in whitelist array, send to that endpoint
  // if not in whitelist array, do not send to that endpoint

  
}
