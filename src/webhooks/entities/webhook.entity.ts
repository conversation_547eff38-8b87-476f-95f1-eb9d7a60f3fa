import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

enum status {
  ACTIVE = 'active',
  PAUSED = 'paused',
  TERMINATED = 'terminated',
  RESUMING = 'resuming',
}

@Entity('webhook')
export class Webhook {
  @PrimaryGeneratedColumn('uuid')
  id: number;

  @Column({ type: 'int' })
  user_id: number;

  @Column({ type: 'varchar'})
  webhook_name: string;

  @Column({ type: 'varchar'})
  webhook_url: string;

  @Column({ type: 'varchar'})
  security_tokens: string // for HMAC signatures;

  @Column({ type: 'varchar', nullable: true })
  start_position: string; // Block number as string
  
  @Column({ type: 'enum', enum: status })
  status: status;

  @Column({ type: 'json', nullable: true })
  whitelisted_endpoints: string[] | null;

  @Column({ type: 'varchar', nullable: true , default: null})
  tags: string;

  @Column({ type: 'boolean', default: true})
  is_active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true  , default: null })
  deleted_at: Date ;
}
