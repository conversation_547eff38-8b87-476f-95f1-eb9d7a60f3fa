import { <PERSON>, Get, Post, Body, Patch, Param, Delete, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';
import { NetworksService } from './networks.service';
import { CreateNetworkDto } from './dto/create-network.dto';
import { UpdateNetworkDto } from './dto/update-network.dto';

@Controller('networks')
export class NetworksController {
  constructor(private readonly networksService: NetworksService) {}

  // Get all networks
  @Get('findAll')
  async findAll() {
    return this.networksService.findAll();
  }

  // Get all active networks
  @Get('findAllActive')
  async findAllActive() {
    return this.networksService.findAllActive();
  }

  // Get grouped networks
  @Get('grouped')
  async findGrouped() {
    return this.networksService.findGrouped();
  }

  // Find mainnets
  @Get('mainnets')
  async findMainnets() {
    return this.networksService.findMainnets();
  }

  // Find testnets
  @Get('testnets')
  async findTestnets() {
    return this.networksService.findTestnets();
  }

  // POST /networks - Create new network
  @Post('create/new')
  async create(@Body() createNetworkDto: CreateNetworkDto) {
    return this.networksService.create(createNetworkDto);
  }

  // Search via ID
  @Get('searchById/:id')
  async findOneWithId(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.findOneWithId(id);
  }

  // Search via ID
  @Get('searchByDisplayName/:display_name')
  async findOneWithName(@Param('display_name') display_name: string) {
    return this.networksService.findOneWithName(display_name);
  }


  // Edit network info
  @Patch('Update/:id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateNetworkDto: UpdateNetworkDto,
  ) {
    return this.networksService.update(id, updateNetworkDto);
  }

  //Delete network
  @Delete('delete/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.remove(id);
  }

  //Soft delete (deactivate)
  @Patch(':id/pause')
  async pause(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.pause(id);
  }

  //  Activate network
  @Patch(':id/activate')
  async activate(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.activate(id);
  }

  @Get('latestBlock/:network_name')
  async getLatestBlockNumber(@Param('network_name') network_name: string) {
    return this.networksService.getLatestBlockNumber(network_name);
  }
}

