import { BadRequestException, Injectable } from '@nestjs/common';
import { NetworkType } from '../entities/network.entity';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import configuration from 'config/configuration';

interface NetworkConfig {
  display_name: string;
  rpc_payload: any;
  type: NetworkType;
  is_active: boolean;
}

// RPC Response interface
interface RPCResponse {
  jsonrpc: string;
  id: number;
  result?: string;
  error?: {
    code: number;
    message: string;
  };
}


@Injectable()
export class EVMHelper  {
  private readonly supportedType = NetworkType.EVM;
  private readonly quicknodeEndpoint: string;
  private readonly quicknodeToken: string;

  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    private configService: ConfigService          //what dis ?
  ) {
    this.quicknodeEndpoint = configuration().qn_endpoint;
    this.quicknodeToken = configuration().qn_endpoint_token;

    if (!this.quicknodeEndpoint || !this.quicknodeToken) {
      throw new Error('QuickNode credentials not configured in environment');
    }
  }

  async getBlockNumber(networkName: string, networkType: NetworkType): Promise<any> {
    try {
      // Check if this network exists
      if (networkType !== this.supportedType) {
        throw new BadRequestException(`EVMHelper only supports ${this.supportedType} networks`);
      }

      // Route to appropriate network handler and return raw response
      const rawResponse = await this.routeToNetwork(networkName);

      return rawResponse;
    } catch (error) {
      throw new BadRequestException(`EVM helper failed for ${networkName}: ${error.message}`);
    }
  }

  private async routeToNetwork(networkName: string): Promise<RPCResponse> {
    const normalizedName = networkName.toLowerCase();

    switch (normalizedName) {
      case 'ethereum-mainnet':
        return await this.ethereumMainnet();
      
      case 'ethereum-sepolia':
        return await this.ethereumSepolia();
      
      case 'ethereum-holesky':
        return await this.ethereumHolesky();
      
      default:
        throw new BadRequestException(`Unsupported EVM network: ${networkName}`);
    }
  }

    // Ethereum Mainnet handler
  private async ethereumMainnet(): Promise<RPCResponse> {
    const networkConfig = await this.getNetworkConfig('ethereum-mainnet');
    const url = this.buildQuickNodeURL('ethereum-mainnet');
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Ethereum Sepolia handler
  private async ethereumSepolia(): Promise<RPCResponse> {
    const networkConfig = await this.getNetworkConfig('ethereum-sepolia');
    const url = this.buildQuickNodeURL('ethereum-sepolia');
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Ethereum Holesky handler
  private async ethereumHolesky(): Promise<RPCResponse> {
    const networkConfig = await this.getNetworkConfig('ethereum-holesky');
    const url = this.buildQuickNodeURL('ethereum-holesky');
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  private async getNetworkConfig(network_name: string): Promise<NetworkConfig> {
    const network = await this.entityManager
      .createQueryBuilder('network', 'n')
      .where('n.display_name = :network_name AND n.is_active = true', { network_name })
      .getOne();

    if (!network) {
      throw new BadRequestException(`Network configuration not found: ${network_name}`);
    }
    return network as NetworkConfig;
  }

  // QuickNode URL builder
  private buildQuickNodeURL(networkName: string): string {
    // ETH mainnet is differently abled ( special is more ways than naught)
    if (networkName === 'ethereum-mainnet') {
      return `https://${this.quicknodeEndpoint}.quiknode.pro/${this.quicknodeToken}/`;
    }
    // Standard URL structure for all other networks
    return `https://${this.quicknodeEndpoint}.${networkName}.quiknode.pro/${this.quicknodeToken}/`;
  }

  // Make the RPC call
  private async makeRPCCall(url: string, payload: any): Promise<RPCResponse> {
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });
      console.log('response', response);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data: RPCResponse = await response.json();

      if (data.error) {
        throw new Error(`RPC Error ${data.error.code}: ${data.error.message}`);
      }
      return data;
    } catch (error) {
      throw new Error(`RPC call failed: ${error.message}`);
    }
  }

  // blocknumber ( 0x1234 ) -> sequence (27364)
  private processBlockNumberResponse(response: RPCResponse, networkName: string): string {
    if (!response.result) {
      throw new Error(`No result in RPC response for ${networkName}`);
    }

    // Convert hex to decimal string
    const blockNumberHex = response.result;
    const blockNumberDecimal = parseInt(blockNumberHex, 16);

    if (isNaN(blockNumberDecimal)) {
      throw new Error(`Invalid hex block number: ${blockNumberHex}`);
    }

    return blockNumberDecimal.toString();
  }

  public getSupportedNetworks(): string[] {
    return [
      'ethereum-mainnet',
      'ethereum-sepolia', 
      'ethereum-holesky'
    ];
  }
}