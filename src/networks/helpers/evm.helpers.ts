import { HttpService } from '@nestjs/axios';
import { BadRequestException, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Network, NetworkType } from '../entities/network.entity';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import configuration from 'config/configuration';
import { firstValueFrom, isObservable, Observable } from 'rxjs';
import { AxiosResponse } from 'axios';

// RPC Response interface
interface RPCResponse {
  jsonrpc: string;
  id: number;
  result?: string;
  error?: {
    code: number;
    message: string;
  };
}

@Injectable()
export class EVMHelper  {
  private readonly supportedType = NetworkType.EVM;
  private readonly quicknodeEndpoint: string;
  private readonly quicknodeToken: string;

  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    private readonly httpService: HttpService
  ) {
    this.quicknodeEndpoint = configuration().qn_endpoint;
    this.quicknodeToken = configuration().qn_endpoint_token;

    if (!this.quicknodeEndpoint || !this.quicknodeToken) {
      throw new Error('QuickNode credentials not configured in environment');
    }
  }

  async getBlockNumber(network_name: string, networkType: NetworkType): Promise<any> {
    try {
      // Check if this network exists
      if (networkType !== this.supportedType) {
        throw new BadRequestException(`EVMHelper only supports ${this.supportedType} networks`);
      }

      // Route to appropriate network handler and return raw response
      const rawResponse = await this.routeToNetwork(network_name);
      console.log("RAWResponse", rawResponse)

      return rawResponse;
    } catch (error) {
      throw new BadRequestException(`EVM helper failed for ${network_name}: ${error.message}`);
    }
  }

  private async routeToNetwork(network_name: string): Promise<RPCResponse> {
    const normalizedName = network_name.toLowerCase();

    switch (normalizedName) {
      case 'ethereum-mainnet':
        return await this.ethereumMainnet();
      
      case 'ethereum-sepolia':
        return await this.ethereumSepolia();
      
      case 'ethereum-holesky':
        return await this.ethereumHolesky();
      
      default:
        throw new BadRequestException(`Unsupported EVM network: ${network_name}`);
    }
  }

    // Ethereum Mainnet handler
  private async ethereumMainnet(): Promise<RPCResponse> {
    const networkConfig = await this.getNetworkConfig('ethereum-mainnet');
    console.log("=============================")
    console.log(networkConfig);

    const url = await this.buildQuickNodeURL('ethereum-mainnet');
    console.log("=============================")
    console.log(url);
    console.log("=============================")
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Ethereum Sepolia handler
  private async ethereumSepolia(): Promise<RPCResponse> {
    const networkConfig = await this.getNetworkConfig('ethereum-sepolia');
    const url = this.buildQuickNodeURL('ethereum-sepolia');
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Ethereum Holesky handler
  private async ethereumHolesky(): Promise<RPCResponse> {
    const networkConfig = await this.getNetworkConfig('ethereum-holesky');
    const url = this.buildQuickNodeURL('ethereum-holesky');
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  private async getNetworkConfig(network_name: string): Promise<any> {
  const network = await this.entityManager
    .createQueryBuilder(Network, 'n')  
    .where('n.display_name = :network_name AND n.is_active = true', { network_name })
    .getOne();

  if (!network) {
    throw new BadRequestException(`Network configuration not found: ${network_name}`);
  }

  return {
    display_name: network.display_name,
    rpc_payload: network.rpc_payload,
    type: network.type,
    is_active: network.is_active,
  };
}


  // QuickNode URL builder
  private buildQuickNodeURL(network_name: string): string {
    // ETH mainnet is differently abled ( special is more ways than naught)
    if (network_name === 'ethereum-mainnet') {
      return `https://${this.quicknodeEndpoint}.quiknode.pro/${this.quicknodeToken}/`;
    }
    // Standard URL structure for all other networks
    return `https://${this.quicknodeEndpoint}.${network_name}.quiknode.pro/${this.quicknodeToken}/`;
  }

  private async makeRPCCall(url: string, payload: any): Promise<RPCResponse> {
    try {
      const response = this.httpService.post(
        url,
        payload,
        { headers: { 'Content-Type': 'application/json' } }
      )
      console.log("response", response);
      return await this.handleRequest(response, 'RPC call');
    } catch (error) {
      throw new Error(`RPC call failed: ${error.message}`);
    }
  }
  // blocknumber ( 0x1234 ) -> sequence (27364)
  private processBlockNumberResponse(response: RPCResponse, network_name: string): string {
    if (!response.result) {
      throw new Error(`No result in RPC response for ${network_name}`);
    }

    // Convert hex to decimal string
    const blockNumberHex = response.result;
    const blockNumberDecimal = parseInt(blockNumberHex, 16);

    if (isNaN(blockNumberDecimal)) {
      throw new Error(`Invalid hex block number: ${blockNumberHex}`);
    }

    return blockNumberDecimal.toString();
  }

  public getSupportedNetworks(): string[] {
    return [
      'ethereum-mainnet',
      'ethereum-sepolia', 
      'ethereum-holesky'
    ];
  }

  protected async handleRequest<T>(
    request: Observable<AxiosResponse<T>> | Promise<AxiosResponse<T>>, // data kena error 
    operation: string
  ): Promise<T> {
    try {
      const response = isObservable(request)
        ? await firstValueFrom(request)
        : await request;

      return response.data;
    } catch (error) {
      const status = error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
      const message = error.response?.data || `${operation} failed`;

      throw new HttpException(message, status);
    }
  }
}